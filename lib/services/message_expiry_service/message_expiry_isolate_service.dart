import 'dart:async';
import 'dart:isolate';

import 'package:event_bus/event_bus.dart';
import 'package:flutter_audio_room/core/di/app_module.dart';
import 'package:flutter_audio_room/core/utils/log_utils.dart';
import 'package:flutter_audio_room/services/isar_service/i_isar_service.dart';
import 'package:flutter_audio_room/services/isar_service/message/isar_message_service.dart';
import 'package:flutter_audio_room/services/message_expiry_service/events/message_expiry_events.dart';

/// Service to manage message expiry deletion using Isolate
class MessageExpiryIsolateService {
  static MessageExpiryIsolateService? _instance;

  Isolate? _isolate;
  SendPort? _isolateSendPort;
  ReceivePort? _receivePort;
  StreamSubscription? _isolateSubscription;
  final EventBus _eventBus;
  bool _isRunning = false;

  MessageExpiryIsolateService._(this._eventBus);

  /// Initialize the service with EventBus
  factory MessageExpiryIsolateService.withEventBus(EventBus eventBus) {
    _instance = MessageExpiryIsolateService._(eventBus);
    return _instance!;
  }

  /// Get singleton instance (must be initialized with EventBus first)
  static MessageExpiryIsolateService get instance {
    if (_instance == null) {
      throw StateError(
          'MessageExpiryIsolateService must be initialized with EventBus first');
    }
    return _instance!;
  }

  /// Start the isolate service
  Future<void> start() async {
    if (_isRunning) {
      LogUtils.d('MessageExpiryIsolateService already running',
          tag: 'MessageExpiryService');
      return;
    }

    try {
      _receivePort = ReceivePort();

      // Start the isolate
      _isolate = await Isolate.spawn(
        _isolateEntryPoint,
        _receivePort!.sendPort,
      );

      // Listen to messages from isolate
      _isolateSubscription = _receivePort!.listen(_handleIsolateMessage);

      _isRunning = true;
      LogUtils.d('MessageExpiryIsolateService started successfully',
          tag: 'MessageExpiryService');
    } catch (e) {
      LogUtils.e('Failed to start MessageExpiryIsolateService: $e',
          tag: 'MessageExpiryService');
      await stop();
      rethrow;
    }
  }

  /// Stop the isolate service
  Future<void> stop() async {
    if (!_isRunning) return;

    try {
      // Send stop command to isolate
      _isolateSendPort?.send({'command': 'stop'});

      // Clean up resources
      await _isolateSubscription?.cancel();
      _isolateSubscription = null;

      _isolate?.kill(priority: Isolate.immediate);
      _isolate = null;

      _receivePort?.close();
      _receivePort = null;

      _isolateSendPort = null;
      _isRunning = false;

      LogUtils.d('MessageExpiryIsolateService stopped',
          tag: 'MessageExpiryService');
    } catch (e) {
      LogUtils.e('Error stopping MessageExpiryIsolateService: $e',
          tag: 'MessageExpiryService');
    }
  }

  /// Trigger immediate expiry check
  void triggerExpiryCheck() {
    if (!_isRunning || _isolateSendPort == null) {
      LogUtils.w(
          'MessageExpiryIsolateService not running, cannot trigger check',
          tag: 'MessageExpiryService');
      return;
    }

    _isolateSendPort!.send({
      'command': 'trigger_check',
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    });
  }

  /// Perform expiry check in main thread using existing Isar instance
  Future<void> performExpiryCheckInMainThread() async {
    try {
      LogUtils.d('Performing expiry check in main thread',
          tag: 'MessageExpiryService');

      // Use existing Isar instance from GetIt
      final isarService = IsarMessageService(getIt<IIsarService>().isar);

      // Query expired messages
      final expiredMessages = await isarService.queryExpiredMessages();
      if (expiredMessages.isNotEmpty) {
        // Delete expired messages
        await isarService.batchDeleteExpiredMessages(
            expiredMessages.map((e) => e.messageId).toList());

        // Group by conversation ID and fire events
        final groupedByConversation = <String, List<String>>{};
        for (final msg in expiredMessages) {
          groupedByConversation
              .putIfAbsent(msg.conversationId, () => [])
              .add(msg.messageId);
        }

        // Fire deletion events for each conversation
        for (final entry in groupedByConversation.entries) {
          _eventBus.fire(ExpiredMessagesDeletedEvent(
            conversationId: entry.key,
            deletedMessageIds: entry.value,
            timestamp: DateTime.now(),
          ));
        }

        LogUtils.d(
            'Deleted ${expiredMessages.length} expired messages across ${groupedByConversation.length} conversations',
            tag: 'MessageExpiryService');
      }

      // Query next expiring message and send to isolate for scheduling
      final nextExpiring = await isarService.queryNextExpiringMessage();
      if (_isolateSendPort != null) {
        _isolateSendPort!.send({
          'command': 'schedule_next_check',
          'nextExpiryTime': nextExpiring?.expiryTime.millisecondsSinceEpoch,
          'timestamp': DateTime.now().millisecondsSinceEpoch,
        });
      }
    } catch (e) {
      LogUtils.e('Error in performExpiryCheckInMainThread: $e',
          tag: 'MessageExpiryService');
    }
  }

  /// Notify about new ephemeral message
  void notifyEphemeralMessageAdded({
    required String conversationId,
    required String messageId,
    required DateTime expiryTime,
  }) {
    if (!_isRunning || _isolateSendPort == null) return;

    _isolateSendPort!.send({
      'command': 'ephemeral_added',
      'conversationId': conversationId,
      'messageId': messageId,
      'expiryTime': expiryTime.millisecondsSinceEpoch,
    });
  }

  /// Notify about ephemeral message marked as read
  void notifyEphemeralMessageRead({
    required String conversationId,
    required String messageId,
    required DateTime readTime,
    required int timeoutSeconds,
  }) {
    if (!_isRunning || _isolateSendPort == null) return;

    _isolateSendPort!.send({
      'command': 'ephemeral_read',
      'conversationId': conversationId,
      'messageId': messageId,
      'readTime': readTime.millisecondsSinceEpoch,
      'timeoutSeconds': timeoutSeconds,
    });
  }

  /// Handle messages from isolate
  void _handleIsolateMessage(dynamic message) {
    try {
      if (message is Map<String, dynamic>) {
        final command = message['command'] as String?;

        switch (command) {
          case 'isolate_ready':
            _isolateSendPort = message['sendPort'] as SendPort;
            LogUtils.d('Isolate ready, SendPort received',
                tag: 'MessageExpiryService');
            break;

          case 'perform_expiry_check':
            // Isolate is requesting main thread to perform expiry check
            performExpiryCheckInMainThread();
            break;

          case 'error':
            LogUtils.e('Isolate error: ${message['error']}',
                tag: 'MessageExpiryService');
            break;

          default:
            LogUtils.w('Unknown command from isolate: $command',
                tag: 'MessageExpiryService');
        }
      }
    } catch (e) {
      LogUtils.e('Error handling isolate message: $e',
          tag: 'MessageExpiryService');
    }
  }



  /// Get service status
  bool get isRunning => _isRunning;
}

/// Entry point for the isolate
void _isolateEntryPoint(SendPort mainSendPort) async {
  final receivePort = ReceivePort();
  Timer? scheduledTimer;
  bool shouldStop = false;

  // Send the SendPort back to main isolate
  mainSendPort.send({
    'command': 'isolate_ready',
    'sendPort': receivePort.sendPort,
  });

  // Listen for commands from main isolate
  receivePort.listen((message) async {
    if (message is Map<String, dynamic>) {
      final command = message['command'] as String?;

      switch (command) {
        case 'stop':
          shouldStop = true;
          scheduledTimer?.cancel();
          receivePort.close();
          break;

        case 'trigger_check':
          _requestExpiryCheck(mainSendPort);
          break;

        case 'schedule_next_check':
          scheduledTimer?.cancel();
          if (!shouldStop) {
            final nextExpiryTime = message['nextExpiryTime'] as int?;
            scheduledTimer =
                _scheduleNextCheckWithTime(mainSendPort, nextExpiryTime);
          }
          break;

        case 'ephemeral_added':
        case 'ephemeral_read':
          // Trigger immediate check when new ephemeral messages are added/read
          _requestExpiryCheck(mainSendPort);
          break;
      }
    }
  });

  // Initial check - main thread will handle scheduling after check
  _requestExpiryCheck(mainSendPort);
}

/// Request main thread to perform expiry check
void _requestExpiryCheck(SendPort mainSendPort) {
  try {
    LogUtils.d('Requesting expiry check from main thread',
        tag: 'MessageExpiryIsolate');

    mainSendPort.send({
      'command': 'perform_expiry_check',
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    });
  } catch (e) {
    LogUtils.e('Error in _requestExpiryCheck: $e', tag: 'MessageExpiryIsolate');
    mainSendPort.send({
      'command': 'error',
      'error': 'Failed to request expiry check: $e',
    });
  }
}

/// Schedule next expiry check based on provided expiry time
Timer? _scheduleNextCheckWithTime(
    SendPort mainSendPort, int? nextExpiryTimeMs) {
  try {
    if (nextExpiryTimeMs != null) {
      final nextExpiryTime =
          DateTime.fromMillisecondsSinceEpoch(nextExpiryTimeMs);
      final delay = nextExpiryTime.difference(DateTime.now());

      if (delay.inMilliseconds > 0) {
        LogUtils.d('Scheduling next check in ${delay.inMinutes} minutes',
            tag: 'MessageExpiryIsolate');
        return Timer(delay, () {
          _requestExpiryCheck(mainSendPort);
        });
      }
    }

    // Fallback: check every minute if no specific expiry time found
    LogUtils.d('Using fallback 1-minute interval for next check',
        tag: 'MessageExpiryIsolate');
    return Timer(const Duration(minutes: 1), () {
      _requestExpiryCheck(mainSendPort);
    });
  } catch (e) {
    LogUtils.e('Error in _scheduleNextCheckWithTime: $e',
        tag: 'MessageExpiryIsolate');
    mainSendPort.send({
      'command': 'error',
      'error': 'Failed to schedule next check: $e',
    });
    return null;
  }
}


